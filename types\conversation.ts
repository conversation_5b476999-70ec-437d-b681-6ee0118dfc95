// Conversation interfaces
export interface ConversationItem {
    _id: string;
    name: string;
    prompt_id: string;
    creat_at: string;
    updated_at: string;
}

export interface ConversationResponse {
    error: boolean;
    status: number;
    msg: string;
    data: {
        conversations: ConversationItem[];
        isFullPage: boolean;
    };
}

export interface UpdateDeleteConversationResponse {
    error: boolean;
    status: number;
    msg: string;
    id: string;
}