import { signOut } from 'next-auth/react'
import { clearAuthTokens } from '@/lib/apiUtils'

/**
 * Performs a complete logout including Google session
 */
export const performCompleteLogout = async () => {
  try {
    // 1. Clear auth tokens
    clearAuthTokens()

    // 2. Clear all storage
    sessionStorage.clear()
    localStorage.clear()

    // 3. Clear all auth-related cookies
    document.cookie.split(";").forEach((c) => {
      const eqPos = c.indexOf("=")
      const name = eqPos > -1 ? c.substring(0, eqPos) : c
      const cookieName = name.trim()

      // Clear NextAuth cookies
      if (cookieName.includes('next-auth') || cookieName.includes('__Secure-next-auth') || cookieName.includes('__Host-next-auth')) {
        document.cookie = `${cookieName}=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/`
        document.cookie = `${cookieName}=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/;domain=${window.location.hostname}`
        document.cookie = `${cookieName}=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/;domain=.${window.location.hostname}`
      }

      // Clear Google OAuth cookies
      if (cookieName.includes('oauth') || cookieName.includes('google') || cookieName.includes('accounts.google')) {
        document.cookie = `${cookieName}=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/`
        document.cookie = `${cookieName}=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/;domain=${window.location.hostname}`
        document.cookie = `${cookieName}=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/;domain=.${window.location.hostname}`
        document.cookie = `${cookieName}=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/;domain=.google.com`
        document.cookie = `${cookieName}=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/;domain=.accounts.google.com`
      }
    })

    // 4. Sign out from NextAuth
    await signOut({
      redirect: false
    })

    // 5. Clear Google session (without redirect for localhost)
    if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
      console.log('🏠 Development environment - redirecting to home')
      window.location.href = '/login'
    } else {
      // For production, use Google logout with proper domain
      const googleLogoutUrl = 'https://accounts.google.com/logout'
      const returnUrl = encodeURIComponent(`${window.location.origin}/`)
      window.location.href = `${googleLogoutUrl}?continue=${returnUrl}`
    }

  } catch (error) {
    console.error('❌ Complete logout error:', error)
    // Fallback: clear everything and redirect to home
    clearAuthTokens()
    sessionStorage.clear()
    localStorage.clear()
    window.location.href = '/login'
  }
}
