/**
 * Utility functions for handling user avatars
 */

/**
 * Tạo avatar URL từ user ID - chỉ trả về nếu có data thật
 * @param userId User ID
 * @param fallbackUrl Fallback URL nếu không có user ID
 * @returns Avatar URL hoặc null
 */
export const createAvatarUrl = (userId?: string | null, fallbackUrl?: string | null): string | null => {
  // Không tạo hardcoded URL - chỉ trả về fallbackUrl nếu có
  return fallbackUrl || null;
};

/**
 * Trích xuất user ID từ profile object
 * @param profile Profile object
 * @returns User ID
 */
export const extractUserId = (profile: any): string | null => {
  if (!profile) return null;

  return profile.user?._id ||
    profile._id ||
    profile.user?.id ||
    profile.id ||
    null;
};

/**
 * Tạo avatar URL từ profile object - chỉ trả về data thật
 * @param profile Profile object
 * @param fallbackUrl Fallback URL
 * @returns Avatar URL hoặc null
 */
export const getAvatarFromProfile = (profile: any, fallbackUrl?: string | null): string | null => {
  const originalAvatar = profile?.user?.avatar || profile?.avatar || profile?.profile_picture;

  // Chỉ trả về avatar thật từ profile hoặc fallbackUrl
  return originalAvatar || fallbackUrl || null;
};
