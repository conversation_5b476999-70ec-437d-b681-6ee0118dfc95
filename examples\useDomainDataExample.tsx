/**
 * Example: <PERSON><PERSON><PERSON> sử dụng useDomainData hooks
 * 
 * <PERSON><PERSON><PERSON> hooks này có thể sử dụng ở bất kỳ đâu trong app mà không cần DomainProvider context
 */

import { useDomainData, usePromptId, useWebsiteId, useWebsiteInfo } from '@/hooks/useDomainData';

// Example 1: L<PERSON>y tất cả domain data
function ExampleFullDomainData() {
  const {
    domainData,
    promptId,
    websiteId,
    websiteInfo,
    hasPromptId,
    hasWebsiteId,
    hasDomainData
  } = useDomainData();

  if (!hasDomainData) {
    return <div>Chưa có domain data</div>;
  }

  return (
    <div>
      <h3>Domain Data:</h3>
      <p>Prompt ID: {promptId}</p>
      <p>Website ID: {websiteId}</p>
      <p>Website Name: {websiteInfo?.meta_title}</p>
      <p>Has Prompt: {hasPromptId ? 'Yes' : 'No'}</p>
    </div>
  );
}

// Example 2: Chỉ cần prompt ID (most common)
function ExamplePromptIdOnly() {
  const promptId = usePromptId();

  if (!promptId) {
    return <div>Thiếu Prompt ID</div>;
  }

  return <div>Current Prompt: {promptId}</div>;
}

// Example 3: Chỉ cần website ID
function ExampleWebsiteIdOnly() {
  const websiteId = useWebsiteId();

  return (
    <div>
      Website ID: {websiteId || 'Not available'}
    </div>
  );
}

// Example 4: Chỉ cần website info
function ExampleWebsiteInfoOnly() {
  const websiteInfo = useWebsiteInfo();

  if (!websiteInfo) {
    return <div>No website info</div>;
  }

  return (
    <div>
      <h3>{websiteInfo.meta_title}</h3>
      <p>{websiteInfo.meta_description}</p>
      <img src={websiteInfo.logo} alt="Logo" />
    </div>
  );
}

// Example 5: Conditional rendering based on domain data
function ExampleConditionalRendering() {
  const { hasPromptId, hasWebsiteId, promptId, websiteId } = useDomainData();

  if (!hasPromptId || !hasWebsiteId) {
    return (
      <div className="error">
        <p>Missing required data:</p>
        {!hasPromptId && <p>- Prompt ID</p>}
        {!hasWebsiteId && <p>- Website ID</p>}
      </div>
    );
  }

  return (
    <div className="success">
      <p>✅ All required data available</p>
      <p>Prompt: {promptId}</p>
      <p>Website: {websiteId}</p>
    </div>
  );
}

// Example 6: Sử dụng trong API calls
function ExampleApiCall() {
  const promptId = usePromptId();
  const websiteId = useWebsiteId();

  const handleApiCall = async () => {
    if (!promptId || !websiteId) {
      console.error('Missing required IDs for API call');
      return;
    }

    try {
      const response = await fetch('/api/some-endpoint', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          prompt_id: promptId,
          website_id: websiteId,
        }),
      });
      
      const data = await response.json();
      console.log('API Response:', data);
    } catch (error) {
      console.error('API Error:', error);
    }
  };

  return (
    <button 
      onClick={handleApiCall}
      disabled={!promptId || !websiteId}
    >
      Call API
    </button>
  );
}

export {
  ExampleFullDomainData,
  ExamplePromptIdOnly,
  ExampleWebsiteIdOnly,
  ExampleWebsiteInfoOnly,
  ExampleConditionalRendering,
  ExampleApiCall
};
