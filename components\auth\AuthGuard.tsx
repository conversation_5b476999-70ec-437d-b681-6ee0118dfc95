"use client";

import React, { useEffect, useState } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { useDomain } from '@/components/providers/DomainProvider';
import { useAuthStore } from '@/stores/authStore';
import { getCookie } from 'cookies-next/client';
import { COOKIE_KEYS } from '@/types/auth';
import { Loader2, Shield } from 'lucide-react';

interface AuthGuardProps {
  children: React.ReactNode;
}

export function AuthGuard({ children }: AuthGuardProps) {
  const router = useRouter();
  const pathname = usePathname();
  const { isVerifying: isDomainVerifying, isAllowed, error: domainError } = useDomain();
  const { isAuthenticated, token, initializeFromCookies } = useAuthStore();
  const [isInitialized, setIsInitialized] = useState(false);

  // Khởi tạo auth store từ cookies
  useEffect(() => {
    if (typeof window !== 'undefined' && !isInitialized) {
      initializeFromCookies();
      setIsInitialized(true);
    }
  }, [initializeFromCookies, isInitialized]);

  // Lắng nghe sự kiện logout để reset state
  useEffect(() => {
    const handleLogout = () => {
      // Reset initialization state để force re-check
      setIsInitialized(false);
      setTimeout(() => {
        initializeFromCookies();
        setIsInitialized(true);
      }, 100);
    };

    // Lắng nghe custom logout event
    window.addEventListener('userLoggedOut', handleLogout);

    return () => {
      window.removeEventListener('userLoggedOut', handleLogout);
    };
  }, [initializeFromCookies]);

  // Kiểm tra token từ cookie
  const cookieToken = getCookie(COOKIE_KEYS.TOKEN);
  const hasValidToken = token || cookieToken;

  useEffect(() => {
    // Chờ cho đến khi auth store được khởi tạo
    if (!isInitialized) {
      return;
    }

    // Nếu đang verify domain, không làm gì
    if (isDomainVerifying) {
      return;
    }

    // Nếu domain không được phép, hiển thị lỗi (không redirect)
    if (!isAllowed) {
      return;
    }

    // Nếu domain đã verify thành công, kiểm tra authentication
    if (isAllowed) {
      // Nếu đã có token (đã login)
      if (hasValidToken && isAuthenticated) {
        // Nếu đang ở trang login, chuyển về home
        if (pathname === '/login') {
          router.push('/');
          return;
        }
        // Nếu ở trang khác, cho phép truy cập
        return;
      }

      // Nếu chưa login và không ở trang login, chuyển đến login
      if (!hasValidToken && pathname !== '/login') {
        router.push('/login');
        return;
      }

      // Nếu ở trang login và chưa login, cho phép ở lại
      if (pathname === '/login' && !hasValidToken) {
        return;
      }
    }
  }, [isInitialized, isDomainVerifying, isAllowed, hasValidToken, isAuthenticated, pathname, router]);

  // Hiển thị loading khi chưa khởi tạo hoặc đang verify domain
  if (!isInitialized || isDomainVerifying) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
        <div className="text-center space-y-6 max-w-md mx-auto p-6">
          <div className="mx-auto w-20 h-20 bg-white rounded-full flex items-center justify-center shadow-xl">
            <Loader2 className="w-10 h-10 text-[#2EAF5D] animate-spin" />
          </div>
          <div className="bg-white rounded-lg p-4 border border-blue-200 space-y-2">
            <div className="flex items-center space-x-2 text-sm text-gray-600">
              <Shield className="w-4 h-4 text-blue-500" />
              <span>Đang xác thực domain...</span>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Hiển thị lỗi domain nếu có
  if (!isAllowed && domainError) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-red-50 to-red-100 flex items-center justify-center">
        <div className="text-center space-y-6 max-w-md mx-auto p-6">
          <div className="mx-auto w-20 h-20 bg-white rounded-full flex items-center justify-center shadow-xl">
            <Shield className="w-10 h-10 text-red-500" />
          </div>
          <div className="bg-white rounded-lg p-4 border border-red-200 space-y-2">
            <h3 className="text-lg font-semibold text-red-700">Lỗi xác thực domain</h3>
            <p className="text-sm text-red-600">{domainError}</p>
          </div>
        </div>
      </div>
    );
  }

  // Hiển thị loading khi đang chuyển hướng
  if (isInitialized && isAllowed && !hasValidToken && pathname !== '/login') {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
        <div className="text-center space-y-6 max-w-md mx-auto p-6">
          <div className="mx-auto w-20 h-20 bg-white rounded-full flex items-center justify-center shadow-xl">
            <Loader2 className="w-10 h-10 text-[#2EAF5D] animate-spin" />
          </div>
          <div className="bg-white rounded-lg p-4 border border-blue-200 space-y-2">
            <p className="text-sm text-blue-600">Đang chuyển hướng đến trang đăng nhập...</p>
          </div>
        </div>
      </div>
    );
  }

  // Hiển thị loading khi đã login và đang chuyển về home
  if (isInitialized && isAllowed && hasValidToken && pathname === '/login') {
    return (
      <div className="min-h-screen bg-gradient-to-br from-green-50 to-green-100 flex items-center justify-center">
        <div className="text-center space-y-6 max-w-md mx-auto p-6">
          <div className="mx-auto w-20 h-20 bg-white rounded-full flex items-center justify-center shadow-xl">
            <Loader2 className="w-10 h-10 text-[#2EAF5D] animate-spin" />
          </div>
          <div className="bg-white rounded-lg p-4 border border-green-200 space-y-2">
            <p className="text-sm text-green-600">Đăng nhập thành công! Đang chuyển hướng...</p>
          </div>
        </div>
      </div>
    );
  }

  // Cho phép render children
  return <>{children}</>;
}
