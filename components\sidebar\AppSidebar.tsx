"use client";

import { Plus, MoreH<PERSON>zon<PERSON>, Edit, Trash2 } from 'lucide-react';
import React, { useState, useRef, useEffect } from 'react';
import { useWebsiteInfo } from '@/hooks/useWebsiteInfo';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Input } from '@/components/ui/input';
import {
  Sidebar,
  SidebarContent,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarRail,
  SidebarSeparator,
} from '@/components/ui/sidebar';
import Image from 'next/image';
import { ConversationItem } from '@/types/conversation';

// ✳️ Interface cho props
interface AppSidebarProps {
  conversations: ConversationItem[];
  setConversations: React.Dispatch<React.SetStateAction<ConversationItem[]>>;
  updateConversationName: (id: string, name: string) => Promise<void>;
  deleteConversationID: (id: string) => Promise<void>;
  currentConversationId: string | null;
  setCurrentConversationId: (id: string | null) => void;
}

export function AppSidebar({
  conversations,
  setConversations, // Giữ để tương thích, dù không sử dụng trực tiếp
  updateConversationName,
  deleteConversationID,
  currentConversationId,
  setCurrentConversationId,
}: AppSidebarProps) {
  const [editingId, setEditingId] = useState<string | null>(null);
  const [editValue, setEditValue] = useState('');
  const [openDropdownId, setOpenDropdownId] = useState<string | null>(null);
  const editInputRef = useRef<HTMLInputElement>(null);

  const { websiteInfo, isLoading } = useWebsiteInfo();

  // Tự động focus và select input khi chỉnh sửa
  useEffect(() => {
    if (editingId && editInputRef.current) {
      requestAnimationFrame(() => {
        requestAnimationFrame(() => {
          if (editInputRef.current) {
            editInputRef.current.focus();
            editInputRef.current.select();
          }
        });
      });
    }
  }, [editingId]);

  const handleEditStart = (conversationId: string, title: string) => {
    setOpenDropdownId(null);
    setEditingId(conversationId);
    setEditValue(title);
  };

  const handleEditSave = async (conversationId: string) => {
    if (editValue.trim()) {
      await updateConversationName(conversationId, editValue.trim());
    }
    setEditingId(null);
    setEditValue('');
  };

  const handleEditCancel = () => {
    setEditingId(null);
    setEditValue('');
  };

  const handleDeleteConversation = async (conversationId: string) => {
    await deleteConversationID(conversationId);
    if (conversationId === currentConversationId) {
      setCurrentConversationId(null);
      window.location.href = '/';
    }
  };

  return (
    <Sidebar variant="inset">
      <div className="flex items-center gap-2 pb-2 pt-0.5 px-2">
        <Image src="/favicon.ico" alt="Logo Chat AI" height={50} width={50} />
      </div>
      <SidebarHeader>
        <div className="px-2">
          <div className="group relative flex cursor-pointer flex-row gap-3 rounded-2xl p-4 transition-all duration-200 ease-in-out bg-white hover:bg-[#e8e8e8a2] dark:bg-gray-900 dark:hover:bg-slate-700 border border-gray-200 dark:border-slate-600">
            <div className="flex overflow-hidden">
              <Image src={websiteInfo.logo} alt="Logo Chat AI" height={32} width={32} />
            </div>
            <div className="flex-1 overflow-hidden">
              <div className="flex items-center gap-1.5">
                <p className="truncate text-sm font-semibold">{websiteInfo.meta_title}</p>
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="16"
                  height="16"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="text-amber-500"
                >
                  <path d="M9.937 15.5A2 2 0 0 0 8.5 14.063l-6.135-1.582a.5.5 0 0 1 0-.962L8.5 9.936A2 2 0 0 0 9.937 8.5l1.582-6.135a.5.5 0 0 1 .963 0L14.063 8.5A2 2 0 0 0 15.5 9.937l6.135 1.581a.5.5 0 0 1 0 .964L15.5 14.063a2 2 0 0 0-1.437 1.437l-1.582 6.135a.5.5 0 0 1-.963 0z" />
                </svg>
              </div>
              <p className="truncate text-xs text-gray-500 dark:text-slate-400">{websiteInfo.meta_description}</p>
            </div>
          </div>
        </div>
      </SidebarHeader>

      <SidebarSeparator className="bg-gray-200 dark:bg-slate-700 my-3" />
      <SidebarContent>
        <SidebarGroup>
          <SidebarGroupContent>
            <SidebarMenu>
              <button
                type="button"
                className="inline-flex items-center justify-center gap-2 mx-2 whitespace-nowrap text-sm font-medium rounded-2xl bg-blue-500 hover:bg-blue-600 py-2 text-white w-[calc(100%-16px)]"
              >
                <Plus className="h-4 w-4" />
                <span>Bắt đầu chủ đề mới</span>
              </button>
              <SidebarGroupLabel className="text-blue-500">Chủ đề AI</SidebarGroupLabel>

              {isLoading ? (
                <SidebarMenuItem>
                  <div className="flex items-center gap-2 px-3 py-3">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                    <span className="text-sm text-white">Đang tải...</span>
                  </div>
                </SidebarMenuItem>
              ) : !Array.isArray(conversations) ? (
                <SidebarMenuItem>
                  <div className="px-3 py-3 text-sm text-red-500">
                    Lỗi: Dữ liệu cuộc trò chuyện không hợp lệ
                  </div>
                </SidebarMenuItem>
              ) : conversations.length === 0 ? (
                <SidebarMenuItem>
                  <div className="px-3 py-3 text-sm text-white/70">
                    Chưa có cuộc trò chuyện nào
                  </div>
                </SidebarMenuItem>
              ) : (
                conversations.map((conversation) => (
                  <SidebarMenuItem key={conversation._id} isActive={conversation._id === currentConversationId}>
                    <SidebarMenuButton
                      isActive={conversation._id === currentConversationId}
                      onClick={() => {
                        if (conversation._id === currentConversationId) return;
                        const newUrl = `/chat/${conversation._id}`;
                        window.history.pushState({ conversationId: conversation._id }, '', newUrl);
                        window.dispatchEvent(
                          new CustomEvent('conversationChange', {
                            detail: { conversationId: conversation._id },
                          })
                        );
                        setCurrentConversationId(conversation._id);
                      }}
                    >
                      <div className="flex items-center gap-3 min-w-0 flex-1">
                        <div className="relative">
                          <Image
                            src="/general_chat.webp"
                            alt="Chat AI"
                            width={24}
                            height={24}
                            className="rounded-full transition-all"
                          />
                        </div>
                        {editingId === conversation._id ? (
                          <Input
                            ref={editInputRef}
                            value={editValue}
                            onChange={(e) => setEditValue(e.target.value)}
                            onBlur={() => handleEditSave(conversation._id)}
                            onKeyDown={(e) => {
                              if (e.key === 'Enter') handleEditSave(conversation._id);
                              else if (e.key === 'Escape') handleEditCancel();
                            }}
                            className="h-6 text-sm border-none p-0 focus-visible:ring-0 focus-visible:ring-offset-0 bg-transparent"
                          />
                        ) : (
                          <span className="truncate text-sm font-medium transition-colors">
                            {conversation.name}
                          </span>
                        )}
                      </div>
                      <DropdownMenu
                        open={openDropdownId === conversation._id}
                        onOpenChange={(open) => setOpenDropdownId(open ? conversation._id : null)}
                      >
                        <DropdownMenuTrigger asChild>
                          <div
                            className="inline-flex items-center justify-center h-6 w-6 p-0 opacity-0 group-hover:opacity-100 transition-opacity rounded hover:bg-slate-600/50 cursor-pointer"
                            onClick={(e) => e.stopPropagation()}
                          >
                            <MoreHorizontal className="h-3 w-3" />
                          </div>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem
                            onClick={(e) => {
                              e.stopPropagation();
                              handleEditStart(conversation._id, conversation.name);
                            }}
                          >
                            <Edit className="h-4 w-4 mr-2" />
                            Sửa tên
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            onClick={(e) => {
                              e.stopPropagation();
                              handleDeleteConversation(conversation._id);
                            }}
                            className="text-destructive"
                          >
                            <Trash2 className="h-4 w-4 mr-2" />
                            Xóa
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </SidebarMenuButton>
                  </SidebarMenuItem>
                ))
              )}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>
      </SidebarContent>
      <SidebarRail />
    </Sidebar>
  );
}