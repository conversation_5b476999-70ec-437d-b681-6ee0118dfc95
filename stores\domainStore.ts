// src/store/domainStore.ts
import { create } from 'zustand';
import { DecodedDomainData } from "@/types/domain";
import { getCookie, setCookie } from 'cookies-next/client';

interface DomainStore {
    decodedDomainData: DecodedDomainData | null;
    setDecodedDomainData: (data: DecodedDomainData) => void;
    clearDecodedDomainData: () => void;

}

export const useDomainStore = create<DomainStore>((set) => {
    return {
        decodedDomainData: null,
        setDecodedDomainData: (data) => set({ decodedDomainData: data }),
        clearDecodedDomainData: () => {
            set({ decodedDomainData: null });
        },

    };
});