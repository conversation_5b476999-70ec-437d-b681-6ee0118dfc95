"use client";

import React, { useState } from 'react';
import { Send } from 'lucide-react';
import { useSession } from 'next-auth/react';
import { useMessageChat } from '@/hooks/useChat';

export default function SendChat() {
    const [input, setInput] = useState('');
    const { data: session } = useSession();

    const handleSend = async () => {
        if (!input.trim()) return;
    }


    return (
        <div className="flex items-center p-2 border-t dark:border-gray-700">
            <input
                type="text"
                value={input}
                onChange={(e) => setInput(e.target.value)}
                onKeyDown={(e) => e.key === 'Enter' && handleSend()}
                placeholder="Nhập tin nhắn..."
                className="flex-1 p-2 rounded-lg border dark:border-gray-600 bg-transparent focus:outline-none"
            />
            <button
                id="sendButton"
                aria-label="Gửi tin nhắn"
                type="button"
                onClick={handleSend}
                className="ml-2 p-2 rounded-full hover:bg-gray-200 dark:hover:bg-gray-700"
                disabled={!input.trim()}
            >
                <Send className="w-5 h-5 text-blue-500" />
            </button>
        </div>
    );
}