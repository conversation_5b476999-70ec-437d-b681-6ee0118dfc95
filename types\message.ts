export interface MessageResponse {
    error: boolean;
    status: number;
    msg: string;
    data: Message[];
}

export interface Message {
    _id: string;
    type: number; // 1: user, 0: AI
    content: string;
    albums: any[];
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
    conversation_id: string;
    created_at: string;
    updated_at: string;
    __v: number;
}