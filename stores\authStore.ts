import { create } from 'zustand';
import { subscribeWithSelector } from 'zustand/middleware';
import { getCookie, setCookie, deleteCookie } from 'cookies-next/client';
import type { AuthState, AuthActions } from '@/types/auth';
import { UserProfile } from '@/types/profile';

// Cookie configuration
const COOKIE_KEYS = {
  TOKEN: 'fchatai_token',
  USER_ID: 'fchatai_user_id',
  SHOP_ID: 'fchatai_shop_id',
} as const;

const COOKIE_OPTIONS = {
  maxAge: 7 * 24 * 60 * 60, // 7 days
  path: '/',
  sameSite: 'lax' as const,
  secure: process.env.NODE_ENV === 'production',
};

// Initial state
const initialState: AuthState = {
  token: null,
  user_id: null,
  shop_id: null,
  prompt_id: null,
  userInfo: null,
  isAuthenticated: false,
  isLoading: false,
  error: null,
  profile: null,
  topics: [],
  conversations: [],
  domainConfig: null,
};

/**
 * Optimized auth store with better separation of concerns
 */
export const useAuthStore = create<AuthState & AuthActions>()(
  subscribeWithSelector((set, get) => ({
    ...initialState,

    // Selectors (computed values)
    getWebsiteId: () => {
      const state = get();
      return state.domainConfig?.config?.website?.website_id;
    },

    getToken: () => {
      const state = get();
      return state.token || getCookie(COOKIE_KEYS.TOKEN) as string || null;
    },

    getUserId: () => {
      const state = get();
      return state.user_id || getCookie(COOKIE_KEYS.USER_ID) as string || null;
    },

    getShopId: () => {
      const state = get();
      return state.shop_id || getCookie(COOKIE_KEYS.SHOP_ID) as string || null;
    },

    // Token management
    setToken: (token: string) => {
      setCookie(COOKIE_KEYS.TOKEN, token, COOKIE_OPTIONS);

      // Update state
      set({
        token,
        isAuthenticated: !!token,
        error: null
      });

      // Dispatch event for cross-component updates
      if (typeof window !== 'undefined') {
        window.dispatchEvent(new CustomEvent('tokenSet', { detail: { token } }));
      }
    },

    clearToken: () => {
      deleteCookie(COOKIE_KEYS.TOKEN);
      set({
        token: null,
        isAuthenticated: false
      });
    },

    // User data management
    setUserData: (data: { user_id?: string; shop_id?: string; prompt_id?: string }) => {
      const updates: Partial<AuthState> = {};

      if (data.user_id) {
        setCookie(COOKIE_KEYS.USER_ID, data.user_id, COOKIE_OPTIONS);
        updates.user_id = data.user_id;
      }

      if (data.shop_id) {
        setCookie(COOKIE_KEYS.SHOP_ID, data.shop_id, COOKIE_OPTIONS);
        updates.shop_id = data.shop_id;
      }

      if (data.prompt_id) {
        updates.prompt_id = data.prompt_id;
      }

      set(updates);
    },

    setUserInfo: (userInfo: UserProfile | null) => {
      set({ userInfo });
    },

    // Profile management
    setProfile: (profile: any) => {
      set({ profile });
    },

    setTopics: (topics: any[]) => {
      set({ topics });
    },

    setConversations: (conversations: any[]) => {
      set({ conversations });
    },

    // Domain configuration
    setDomainConfig: (domainConfig: any) => {
      set({ domainConfig });
    },

    // State management
    setLoading: (isLoading: boolean) => {
      set({ isLoading });
    },

    setError: (error: string | null) => {
      set({ error });
    },

    // Clear all data
    clearAll: () => {
      // Clear cookies
      deleteCookie(COOKIE_KEYS.TOKEN);
      deleteCookie(COOKIE_KEYS.USER_ID);
      deleteCookie(COOKIE_KEYS.SHOP_ID);

      // Reset state
      set(initialState);
    },

    // Initialize from cookies
    initializeFromCookies: () => {
      const token = getCookie(COOKIE_KEYS.TOKEN) as string;
      const user_id = getCookie(COOKIE_KEYS.USER_ID) as string;
      const shop_id = getCookie(COOKIE_KEYS.SHOP_ID) as string;

      if (token || user_id || shop_id) {
        set({
          token: token || null,
          user_id: user_id || null,
          shop_id: shop_id || null,
          isAuthenticated: !!token,
        });

      }
    },

    // Legacy methods for backward compatibility
    setPromptId: (prompt_id: string) => {
      set({ prompt_id });
    },
  }))
);

// Selectors for optimized component subscriptions
export const authSelectors = {
  token: (state: AuthState & AuthActions) => state.token,
  isAuthenticated: (state: AuthState & AuthActions) => state.isAuthenticated,
  userInfo: (state: AuthState & AuthActions) => state.userInfo,
  profile: (state: AuthState & AuthActions) => state.profile,
  isLoading: (state: AuthState & AuthActions) => state.isLoading,
  error: (state: AuthState & AuthActions) => state.error,

  // Computed selectors
  hasValidAuth: (state: AuthState & AuthActions) =>
    state.isAuthenticated && !!state.token && !!state.user_id,

  userCredits: (state: AuthState & AuthActions) => ({
    credit: state.profile?.credit || state.profile?.user?.credit || 0,
    credit_use: state.profile?.credit_use || state.profile?.user?.credit_use || 0,
    credit_bonus: state.profile?.credit_bonus || state.profile?.package?.credits_bonus || 0,
  }),
};

// Initialize store from cookies on load
if (typeof window !== 'undefined') {
  useAuthStore.getState().initializeFromCookies();
}
