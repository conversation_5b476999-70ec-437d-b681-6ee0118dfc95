"use client";

import { useSession } from 'next-auth/react';
import { getPromptIdWithFallback, getStoredShopId } from '@/lib/apiUtils';
import type { MainContentProps } from '@/types/components';
import './main-ai-bg.css';
import ChatInterface from '@components/chat/ChatInterface';

export default function MainContent({ }: MainContentProps) {
    const { data: session } = useSession();

    const promptId = getPromptIdWithFallback();
    const shopId = getStoredShopId() || '';
    const userId = session?.user?.id || '';

    return (
        <main
            className="flex-1 flex flex-col items-center justify-center relative bg-white dark:bg-gray-900 overflow-hidden"
            role="main"
        >
            <div className="relative flex h-full w-full flex-col overflow-hidden bg-white dark:bg-gray-900">
                <ChatInterface
                    promptId={promptId}
                    shopId={shopId}
                    userId={userId}
                />
            </div>
        </main>
    );
}