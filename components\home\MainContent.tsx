"use client";

import { useSession } from 'next-auth/react';
import { getStoredShopId } from '@/lib/apiUtils';
import type { MainContentProps } from '@/types/components';
import './main-ai-bg.css';
import ChatInterface from '@components/chat/ChatInterface';
import { useDomain } from '@/components/providers/DomainProvider';

export default function MainContent({ }: MainContentProps) {
    const { data: session } = useSession();
    const { domainData } = useDomain();

    // Lấy prompt_id trực tiếp từ domainData
    const promptId = domainData?.prompt?._id || null;
    const shopId = getStoredShopId();
    const userId = session?.user?.id || '';

    // Nếu không có promptId hoặc shopId thì không render ChatInterface
    if (!promptId || !shopId) {
        return (
            <main
                className="flex-1 flex flex-col items-center justify-center relative bg-white dark:bg-gray-900 overflow-hidden"
                role="main"
            >
                <div className="text-center space-y-4">
                    <div className="text-gray-500">
                        <PERSON><PERSON> tải cấu hình...
                    </div>
                    <div className="text-sm text-gray-400">
                        {!promptId && "Thiếu prompt ID"}
                        {!shopId && "Thiếu shop ID"}
                    </div>
                </div>
            </main>
        );
    }

    return (
        <main
            className="flex-1 flex flex-col items-center justify-center relative bg-white dark:bg-gray-900 overflow-hidden"
            role="main"
        >
            <div className="relative flex h-full w-full flex-col overflow-hidden bg-white dark:bg-gray-900">
                <ChatInterface
                    promptId={promptId}
                    shopId={shopId}
                    userId={userId}
                />
            </div>
        </main>
    );
}