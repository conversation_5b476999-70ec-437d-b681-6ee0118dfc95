import { useAuthStore } from '@/stores/authStore';
import { DecodedDomainData } from '@/types/domain';

/**
 * Hook để truy cập domain data từ authStore một cách dễ dàng
 * <PERSON><PERSON> thể sử dụng ở bất kỳ đâu mà không cần DomainProvider context
 */
export const useDomainData = () => {
  const domainData = useAuthStore((state) => state.domainData);

  return {
    // Raw domain data
    domainData,
    
    // Computed values for easy access
    promptId: domainData?.prompt?._id || null,
    websiteId: domainData?.website?.website_id || null,
    websiteInfo: domainData?.prompt?.website || null,
    
    // Helper functions
    getPromptId: () => domainData?.prompt?._id || null,
    getWebsiteId: () => domainData?.website?.website_id || null,
    getWebsiteInfo: () => domainData?.prompt?.website || null,
    
    // Status checks
    hasPromptId: !!domainData?.prompt?._id,
    hasWebsiteId: !!domainData?.website?.website_id,
    hasWebsiteInfo: !!domainData?.prompt?.website,
    hasDomainData: !!domainData,
  };
};

/**
 * Hook để lấy chỉ prompt ID (most common use case)
 */
export const usePromptId = () => {
  const domainData = useAuthStore((state) => state.domainData);
  return domainData?.prompt?._id || null;
};

/**
 * Hook để lấy chỉ website ID
 */
export const useWebsiteId = () => {
  const domainData = useAuthStore((state) => state.domainData);
  return domainData?.website?.website_id || null;
};

/**
 * Hook để lấy website info
 */
export const useWebsiteInfo = () => {
  const domainData = useAuthStore((state) => state.domainData);
  return domainData?.prompt?.website || null;
};
