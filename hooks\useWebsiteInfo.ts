"use client"

import { useDomain } from '@/components/providers/DomainProvider'

export interface WebsiteInfo {
  meta_title: string
  meta_description: string
  favicon: string
  logo: string
  thumbnail: string
  domain?: string
  status?: number
}

/**
 * Hook để lấy thông tin website từ domain data thật
 */
export const useWebsiteInfo = () => {
  const { domainData, isVerifying } = useDomain()

  // Lấy thông tin website từ domain data thật
  const websiteInfo = domainData?.prompt?.website

  // Default values nếu không có data
  const defaultInfo: WebsiteInfo = {
    meta_title: "FChat AI",
    meta_description: "Tr<PERSON> lý AI thông minh",
    favicon: "/favicon.ico",
    logo: "/favicon.ico",
    thumbnail: "/favicon.ico"
  }

  // Nếu có data thật từ API, sử dụng data đó, nếu không thì trả về null
  const finalInfo: WebsiteInfo | null = websiteInfo ? {
    meta_title: websiteInfo.meta_title || defaultInfo.meta_title,
    meta_description: websiteInfo.meta_description || defaultInfo.meta_description,
    favicon: websiteInfo.favicon || defaultInfo.favicon,
    logo: websiteInfo.logo || defaultInfo.logo,
    thumbnail: websiteInfo.thumbnail || defaultInfo.thumbnail,
    domain: websiteInfo.domain,
    status: websiteInfo.status
  } : null

  return {
    websiteInfo: finalInfo || defaultInfo,
    hasCustomInfo: !!websiteInfo,
    isLoading: isVerifying,
    domainData,
    // Utility functions
    getTitle: () => finalInfo?.meta_title || defaultInfo.meta_title,
    getDescription: () => finalInfo?.meta_description || defaultInfo.meta_description,
    getLogo: () => finalInfo?.logo || finalInfo?.favicon || defaultInfo.logo,
    getFavicon: () => finalInfo?.favicon || defaultInfo.favicon,
    getThumbnail: () => finalInfo?.thumbnail || finalInfo?.logo || finalInfo?.favicon || defaultInfo.thumbnail,
  }
}
