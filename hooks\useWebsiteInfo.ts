"use client"

import { useDomain } from '@/components/providers/DomainProvider'

export interface WebsiteInfo {
  meta_title: string
  meta_description: string
  favicon: string
  logo: string
  thumbnail: string
  domain?: string
  status?: number
}

/**
 * Hook để lấy thông tin website từ prompt.website
 */
export const useWebsiteInfo = () => {
  const { domainConfig } = useDomain()

  // Lấy thông tin website từ domainConfig
  const websiteInfo = domainConfig?.prompt?.website

  // Default values
  const defaultInfo: WebsiteInfo = {
    meta_title: websiteInfo?.meta_title || "localhost",
    meta_description: websiteInfo?.meta_description || "test",
    favicon: websiteInfo?.favicon || "/favicon.ico",
    logo: websiteInfo?.logo || "/favicon.ico",
    thumbnail: websiteInfo?.thumbnail || "/favicon.ico"
  }

  // Merge với default values
  const finalInfo: WebsiteInfo = {
    ...defaultInfo,
    ...websiteInfo
  }

  return {
    websiteInfo: finalInfo,
    hasCustomInfo: !!websiteInfo,
    isLoading: !domainConfig,
    domainConfig,
    // Utility functions
    getTitle: () => finalInfo.meta_title,
    getDescription: () => finalInfo.meta_description,
    getLogo: () => finalInfo.logo || finalInfo.favicon,
    getFavicon: () => finalInfo.favicon,
    getThumbnail: () => finalInfo.thumbnail || finalInfo.logo || finalInfo.favicon,
  }
}
