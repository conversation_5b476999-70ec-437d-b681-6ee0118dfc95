"use client";

import React, { useEffect, useRef } from 'react';
import { useMessageChat } from '@/hooks/useChat';
import { MessageList } from './MessageList';
import { useWebsiteInfo } from '@/hooks/useWebsiteInfo';
import { useConversationState } from '@/hooks/useConversationState';
import SendChat from './sendChat';
interface ChatInterfaceProps {
  conversationId?: string;
  promptId: string | null;
  shopId: string | null;
  userId: string;
  onConversationUpdate?: (oldId: string, newId: string, newName: string) => void;
}

export default function ChatInterface({
  conversationId: propConversationId,
  promptId,
  shopId,
  userId,
  onConversationUpdate,
}: ChatInterfaceProps) {
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const { websiteInfo } = useWebsiteInfo();
  const { currentConversationId } = useConversationState();

  // Sử dụng global state nếu không có prop conversationId
  const conversationId = propConversationId || currentConversationId || '0';

  // Nếu không có promptId hoặc shopId thì không fetch messages
  const shouldFetchMessages = promptId && shopId;

  const {
    messages,
    isLoading: isLoadingMessages,
    error: chatError,
    fetchMessages,
  } = useMessageChat(
    shouldFetchMessages ? conversationId : null,
    promptId,
    shopId,
    userId
  );

  // Tải tin nhắn khi conversationId thay đổi
  useEffect(() => {
    if (!conversationId || conversationId === '0') {
      return;
    }

    fetchMessages();
  }, [conversationId, fetchMessages]);

  // Tự động cuộn xuống tin nhắn mới nhất
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  if (chatError) {
    return (
      <div className="flex items-center justify-center h-full">
        <p className="text-red-500">Lỗi: {chatError}</p>
      </div>
    );
  }

  if (!conversationId || conversationId === '0') {
    return (
      <div className="flex items-center justify-center h-full">
        <p className="text-gray-500">Chọn một cuộc trò chuyện để bắt đầu</p>
      </div>
    );
  }

  return (
    <div className="flex flex-col h-full">
      <div className="relative flex-1">
        <MessageList
          messages={messages}
          isLoading={isLoadingMessages}
          messagesEndRef={messagesEndRef}
        />
      </div>
      <div className="flex flex-col w-full transition-all duration-300 md:p-4 md:pb-0">
        <SendChat />
        <p className="my-1 px-2 text-center text-xs text-gray-500 md:my-2 dark:text-gray-400">
          {websiteInfo.meta_title} có thể mắc sai sót, vui lòng xác thực các thông tin quan trọng
        </p>
      </div>
    </div>
  );
}