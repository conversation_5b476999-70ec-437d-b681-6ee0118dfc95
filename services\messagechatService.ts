import { EP, QUERY } from "@/configs/constants/api";
import { getApiEndpoint } from "@/helpers/handleApiUrl";
import { COOKIE_KEYS } from "@/types/auth";
import { getCookie } from "cookies-next";
import axiosClient from "@/lib/axios";
import { MessageResponse } from "@/types/message";

const token = getCookie(COOKIE_KEYS.TOKEN) as string;
//lấy danh sách message conversation
export const getMessageConversation = async (conversation_id: string) => {
    try {
        const { data } = await axiosClient(process.env.NEXT_PUBLIC_NODE_API_BACKEND_URL, token)
            .get<MessageResponse>(getApiEndpoint([EP.API, EP.V1, EP.MESSAGE, EP.CONVERSATION], {
                [QUERY.CONVERSATION_ID]: conversation_id
            }));
        return data;
    } catch (error) {
        return { error: true, status: 500, msg: 'Failed to load messages', data: [] };
    }
}

interface PayloadChat {
    conversation_id: string;
    query: string;
    prompt_id: string;
    shop_id: string;
    version: string;
    user_id: string;
    website_id: string;
}
export const postChat = async (payload: PayloadChat) => {
    try {
        const { data } = await axiosClient(process.env.NEXT_PUBLIC_NODE_API_BACKEND_URL, token)
            .post<MessageResponse>(getApiEndpoint([EP.API, EP.V1, EP.ASSISTANT, EP.CHAT]), payload);
        return data;
    } catch (error) {
        return { error: true, status: 500, msg: 'Failed to load messages', data: [] };
    }
}
