export interface PackageProfile {
  credit: number;
  credit_use: number;
}

export interface ShopProfile {
  _id: string;
  name: string;
  email: string;
  logo: string
  description: string
  address: string
  phone: string
  status: number
}

export interface UserProfile {
  _id: string;
  name: string;
  email: string;
  avatar?: string | null;
}

export interface ProfileData {
  user: UserProfile;
  shop?: ShopProfile;
  package?: PackageProfile;
}

export interface UserProfileResponse {
  error: boolean;
  status: number;
  msg: string;
  data: ProfileData;
}