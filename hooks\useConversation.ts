import { useEffect, useState } from 'react'
import { ConversationItem } from '@/types/conversation'
import {
  getConversation,
  updateConversation,
  deleteConversation,
} from '@/services/conversationService'
import { useAuthStore } from '@/stores/authStore'

export const useConversation = () => {
  const [loading, setLoading] = useState<boolean>(true)
  const [error, setError] = useState<string | null>(null)
  const [conversations, setConversations] = useState<ConversationItem[]>([])
  const prompt_id = useAuthStore.getState().prompt_id;
  useEffect(() => {
    const fetchConversations = async () => {
      if (!prompt_id) return;
      try {
        setLoading(true)
        const response = await getConversation(prompt_id) // expected to return ConversationResponse
        if (!response.error && Array.isArray(response.data.conversations)) {
          setConversations(response.data.conversations)
        } else {
          setError(response.msg || 'Failed to load conversations')
          setConversations([])
        }
      } catch (err: any) {
        console.error('Error fetching conversations:', err)
        setError(err?.message || 'Failed to load conversations')
        setConversations([])
      } finally {
        setLoading(false)
      }
    }

    fetchConversations()
  }, [prompt_id])

  const updateConversationName = async (conversationId: string, name: string) => {
    try {
      const response = await updateConversation({ id: conversationId, name })
      if (!response.error) {
        setConversations(prev =>
          prev.map(c => (c._id === conversationId ? { ...c, name } : c))
        )
      } else {
        console.error('Update failed:', response.msg)
      }
    } catch (err) {
      console.error('Failed to update conversation name:', err)
    }
  }

  const deleteConversationID = async (conversationId: string) => {
    try {
      const response = await deleteConversation(conversationId)
      if (!response.error) {
        setConversations(prev => prev.filter(c => c._id !== conversationId))
      } else {
        console.error('Delete failed:', response.msg)
      }
    } catch (err) {
      console.error('Failed to delete conversation:', err)
    }
  }

  return {
    conversations,
    setConversations,
    updateConversationName,
    deleteConversationID,
    loading,
    error,
  }
}
