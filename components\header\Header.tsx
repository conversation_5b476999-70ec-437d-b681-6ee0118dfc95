"use client"

import UserDropdown from '@/components/profile/UserDropdown';
import { SidebarTrigger } from '@/components/ui/sidebar';
import { ThemeToggle } from '@/components/ui/ThemeToggle';
import { WebsiteBranding } from '@/components/ui/WebsiteBranding';


export default function Header() {
    return (
        <header className="sticky top-0 z-20 w-full border-b border-gray-200 bg-white dark:border-slate-700 dark:bg-gray-900 flex items-center justify-between px-4 py-1.5">
            <div className="flex items-center gap-3">
                <SidebarTrigger className="-ml-1" />
                <WebsiteBranding
                    logoSize={46}
                    showTitle={true}
                    className="font-bold text-gray-800 sm:text-lg md:text-xl dark:text-slate-100"
                />
            </div>

            <div className="flex items-center justify-end gap-3 min-w-[200px]">
                <ThemeToggle />
                <UserDropdown />
            </div>
        </header>
    );
}
