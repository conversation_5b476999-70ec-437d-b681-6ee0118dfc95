"use client";

import React, { createContext, useContext, useEffect, useState, ReactNode } from "react";
import { verifyDomain, PayloadVerifyDomain } from "@/services/authService";
import { useDomainStore } from "@/stores/domainStore";
import { useAuthStore } from "@/stores/authStore";
import { DecodedDomainData } from "@/types/domain";

// Types
interface DomainContextType {
  isVerifying: boolean;
  isAllowed: boolean;
  error: string | null;
  verificationStep: string;
  retryVerification: () => void;
  domainData: DecodedDomainData | null;
}

// Context
const DomainContext = createContext<DomainContextType>({
  isVerifying: true,
  isAllowed: false,
  error: null,
  verificationStep: 'Khởi tạo...',
  retryVerification: () => { },
  domainData: null
});

// Hook để sử dụng context
export function useDomain() {
  return useContext(DomainContext);
}

interface DomainProviderProps {
  children: ReactNode;
}

export function DomainProvider({ children }: DomainProviderProps) {
  // Sử dụng domainStore
  const {
    decodedDomainData,
    setDecodedDomainData,
  } = useDomainStore();

  const [isVerifying, setIsVerifying] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [verificationStep, setVerificationStep] = useState('Khởi tạo...');

  const verifyCurrentDomain = async () => {
    try {
      setIsVerifying(true);
      setError(null);
      setVerificationStep('Đang xác thực domain...');

      let currentDomain = '';

      if (typeof window !== 'undefined') {
        currentDomain = window.location.hostname;

        // Nếu là localhost thì dùng domain test
        if (currentDomain === "localhost" || currentDomain === "127.0.0.1") {
          currentDomain = process.env.NEXT_PUBLIC_TEST_DOMAIN || "agent.trinhxuanthuy.id.vn";
        }
      }

      setVerificationStep(`Đang kiểm tra domain: ${currentDomain}`);

      const result = await verifyDomain({ domain: currentDomain });

      if (!result) {
        throw new Error("Không thể xác thực domain");
      }

      // Lưu vào domainStore
      setDecodedDomainData(result);

      // Lưu prompt_id vào auth store
      if (result.prompt?._id) {
        const authStore = useAuthStore.getState();
        if (authStore.setPromptId) {
          authStore.setPromptId(result.prompt._id);
        }
      }


      setVerificationStep('Xác thực thành công!');

    } catch (error: any) {
      console.error("❌ Domain verification failed:", error);
      setError(error.message || "Lỗi xác thực domain");
    } finally {
      setIsVerifying(false);
    }
  };

  useEffect(() => {
    verifyCurrentDomain();
  }, []);

  const retryVerification = () => {
    verifyCurrentDomain();
  };

  const contextValue: DomainContextType = {
    isVerifying,
    isAllowed: !!decodedDomainData && !error,
    error,
    verificationStep,
    retryVerification,
    domainData: decodedDomainData
  };

  return (
    <DomainContext.Provider value={contextValue}>
      {children}
    </DomainContext.Provider>
  );
}
