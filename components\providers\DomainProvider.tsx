"use client";

import React, { createContext, useContext, useEffect, useState, ReactNode } from "react";
import { verifyDomain, PayloadVerifyDomain } from "@/services/authService";
import { useDomainStore } from "@/stores/domainStore";
import { useAuthStore } from "@/stores/authStore";
import { DecodedDomainData } from "@/types/domain";

// Types
interface DomainContextType {
  domainConfig: DecodedDomainData | null;
  isVerifying: boolean;
  isAllowed: boolean;
  error: string | null;
  verificationStep: string;
  retryVerification: () => void;
}

// Context
const DomainContext = createContext<DomainContextType>({
  domainConfig: null,
  isVerifying: true,
  isAllowed: false,
  error: null,
  verificationStep: 'Khởi tạo...',
  retryVerification: () => { }
});

// Hook để sử dụng context
export function useDomain() {
  return useContext(DomainContext);
}

interface DomainProviderProps {
  children: ReactNode;
}

export function DomainProvider({ children }: DomainProviderProps) {
  // Sử dụng domainStore
  const {
    decodedDomainData,
    isDomainVerified,
    setDecodedDomainData,
    setIsDomainVerified
  } = useDomainStore();

  const [isVerifying, setIsVerifying] = useState(!isDomainVerified);
  const [error, setError] = useState<string | null>(null);
  const [verificationStep, setVerificationStep] = useState('Khởi tạo...');

  // Lưu vào authStore để các component khác sử dụng
  const setDomainConfig_AuthStore = useAuthStore((state) => state.setDomainConfig);

  const verifyCurrentDomain = async () => {
    try {
      // Nếu đã verified thì không cần verify lại
      if (isDomainVerified && decodedDomainData) {
        console.log("✅ Domain already verified");
        setIsVerifying(false);
        setError(null);
        setVerificationStep('Đã xác thực');
        return;
      }

      setIsVerifying(true);
      setError(null);
      setVerificationStep('Đang xác thực domain...');

      let currentDomain = '';

      if (typeof window !== 'undefined') {
        currentDomain = window.location.hostname;

        // Nếu là localhost thì dùng domain test
        if (currentDomain === "localhost" || currentDomain === "127.0.0.1") {
          currentDomain = process.env.NEXT_PUBLIC_TEST_DOMAIN || "agent.trinhxuanthuy.id.vn";
        }
      }

      console.log("🔍 Verifying domain:", currentDomain);
      setVerificationStep(`Đang kiểm tra domain: ${currentDomain}`);

      const result = await verifyDomain({ domain: currentDomain });

      if (!result) {
        throw new Error("Không thể xác thực domain");
      }

      // Lưu vào domainStore
      setDecodedDomainData(result);
      setIsDomainVerified(true);

      // Lưu vào authStore để các component khác sử dụng
      if (result.website) {
        setDomainConfig_AuthStore(result);
      }

      // Lưu prompt_id vào auth store
      if (result.prompt?._id) {
        const authStore = useAuthStore.getState();
        if (authStore.setPromptId) {
          authStore.setPromptId(result.prompt._id);
        }
      }

      console.log("✅ Domain verification successful:", {
        domain: currentDomain,
        prompt_id: result.prompt?._id,
        website_id: result.website?.website_id
      });

      setVerificationStep('Xác thực thành công!');

    } catch (error: any) {
      console.error("❌ Domain verification failed:", error);
      setError(error.message || "Lỗi xác thực domain");
    } finally {
      setIsVerifying(false);
    }
  };

  useEffect(() => {
    verifyCurrentDomain();
  }, [isDomainVerified]);

  const retryVerification = () => {
    verifyCurrentDomain();
  };

  const contextValue: DomainContextType = {
    domainConfig: decodedDomainData,
    isVerifying,
    isAllowed: !!decodedDomainData && !error,
    error,
    verificationStep,
    retryVerification
  };

  return (
    <DomainContext.Provider value={contextValue}>
      {children}
    </DomainContext.Provider>
  );
}
