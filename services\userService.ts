import { EP } from "@/configs/constants/api";
import { getCookie, setCookie } from 'cookies-next/client';
import { COOKIE_KEYS } from '@/types/auth';
import { useUserStore } from "@/stores/userStore";
import axiosClient from "@/lib/axios";
import { getApiEndpoint } from "@/helpers/handleApiUrl";
import { UserProfileResponse } from "@/types/profile";

export const getUserProfile = async () => {
    const { setProfile, clearProfile } = useUserStore.getState();

    try {
        useUserStore.setState({ isLoading: true });
        const token = getCookie(COOKIE_KEYS.TOKEN) as string;

        if (!token) {
            throw new Error('No authentication token found');
        }

        const { data } = await axiosClient(process.env.NEXT_PUBLIC_NODE_API_BACKEND_URL, token)
            .get<UserProfileResponse>(getApiEndpoint([EP.API, EP.V1, EP.USER, EP.PROFILE]));
        setProfile(data);
        setCookie(COOKIE_KEYS.USER_ID, data.data.user._id);
        return data;
    } catch (error) {
        console.error('❌ getUserProfile: Failed to load profile:', error);
        clearProfile();
        throw error;
    } finally {
        // Clear loading state
        useUserStore.setState({ isLoading: false });
    }
}

