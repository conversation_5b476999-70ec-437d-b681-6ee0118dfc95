export interface DataResponseType<T = any> {
  error?: boolean;
  status: number;
  message?: string;
  msg?: string;
  data?: T;
  id?: string;
}

/**
 * Paginated API response
 */
export interface PaginatedResponse<T = any> extends DataResponseType<T[]> {
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

/**
 * API error response
 */
export interface ApiError {
  success: false;
  message: string;
  error: string;
  statusCode: number;
}

/**
 * Pagination parameters for API requests
 */
export interface PaginationParams {
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: "asc" | "desc";
}



// HTTP methods
export type HttpMethod = 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';

// API endpoints
export interface ApiEndpoints {
  auth: {
    login: string;
    register: string;
    logout: string;
    refresh: string;
  };
  profile: {
    get: string;
    update: string;
  };
  conversations: {
    list: string;
    create: string;
    get: (id: string) => string;
    update: (id: string) => string;
    delete: (id: string) => string;
  };
  messages: {
    list: (conversationId: string) => string;
    send: string;
  };
}

// Request configuration
export interface RequestConfig {
  method: HttpMethod;
  headers?: Record<string, string>;
  body?: any;
  timeout?: number;
  retries?: number;
}
