"use client";

import { useParams, useRouter } from 'next/navigation';
import { useState, useEffect } from 'react';
import Header from '@/components/header/Header';
import MainContent from '@/components/home/<USER>';
import { AppSidebar } from '@/components/sidebar/AppSidebar';
import { SidebarProvider, SidebarInset } from '@/components/ui/sidebar';
import { useConversation } from '@/hooks/useConversation';

export default function ChatPage() {
    const params = useParams();
    const router = useRouter();

    const { conversations, setConversations, updateConversationName, deleteConversationID, loading, error } = useConversation();
    const [currentConversationId, setCurrentConversationId] = useState<string | null>(params.id as string | null);

    // Lấy conversationId từ URL
    const urlConversationId = params.id as string | null;

    // Đ<PERSON>ng bộ currentConversationId với URL khi tải trang
    useEffect(() => {
        if (urlConversationId && urlConversationId !== currentConversationId) {
            setCurrentConversationId(urlConversationId);
        }
    }, [urlConversationId]);

    // Lắng nghe sự kiện thay đổi conversation từ AppSidebar
    useEffect(() => {
        const handleConversationChange = (event: CustomEvent) => {
            const { conversationId } = event.detail;
            if (conversationId !== currentConversationId) {
                setCurrentConversationId(conversationId);
                router.push(`/chat/${conversationId}`);
            }
        };

        window.addEventListener('conversationChange', handleConversationChange as EventListener);
        return () => {
            window.removeEventListener('conversationChange', handleConversationChange as EventListener);
        };
    }, [currentConversationId, router]);

    // Hiển thị loading khi đang tải conversations
    if (loading) {
        return (
            <div className="flex items-center justify-center min-h-screen">
                <div className="text-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-4"></div>
                    <p>Đang tải...</p>
                </div>
            </div>
        );
    }

    // Hiển thị lỗi nếu có
    if (error) {
        return (
            <div className="flex items-center justify-center min-h-screen">
                <div className="text-center">
                    <p className="text-red-500">Lỗi: {error}</p>
                </div>
            </div>
        );
    }

    return (
        <SidebarProvider>
            <AppSidebar
                conversations={conversations}
                setConversations={setConversations}
                currentConversationId={currentConversationId}
                setCurrentConversationId={setCurrentConversationId}
                updateConversationName={updateConversationName}
                deleteConversationID={deleteConversationID}
            />
            <SidebarInset>
                <div className="flex flex-1 flex-col">
                    <Header />
                    <MainContent
                        conversations={conversations}
                        setConversations={setConversations}
                        currentConversationId={currentConversationId}
                        setCurrentConversationId={setCurrentConversationId}
                    />
                </div>
            </SidebarInset>
        </SidebarProvider>
    );
}