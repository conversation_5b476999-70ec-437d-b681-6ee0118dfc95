"use client";

import { useParams } from 'next/navigation';
import { useEffect } from 'react';
import Header from '@/components/header/Header';
import MainContent from '@/components/home/<USER>';
import { AppSidebar } from '@/components/sidebar/AppSidebar';
import { SidebarProvider, SidebarInset } from '@/components/ui/sidebar';
import { useConversation } from '@/hooks/useConversation';
import { useConversationState } from '@/hooks/useConversationState';

export default function ChatPage() {
    const params = useParams();
    const { conversations, setConversations, updateConversationName, deleteConversationID, loading, error } = useConversation();
    const { currentConversationId, setCurrentConversationId } = useConversationState();

    // Lấy conversationId từ URL
    const urlConversationId = params.id as string | null;

    // Đồng bộ global state với URL khi tải trang
    useEffect(() => {
        if (urlConversationId && urlConversationId !== currentConversationId) {
            setCurrentConversationId(urlConversationId);
        }
    }, [urlConversationId, currentConversationId, setCurrentConversationId]);

    // Hiển thị loading khi đang tải conversations
    if (loading) {
        return (
            <div className="flex items-center justify-center min-h-screen">
                <div className="text-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-4"></div>
                    <p>Đang tải...</p>
                </div>
            </div>
        );
    }

    // Hiển thị lỗi nếu có
    if (error) {
        return (
            <div className="flex items-center justify-center min-h-screen">
                <div className="text-center">
                    <p className="text-red-500">Lỗi: {error}</p>
                </div>
            </div>
        );
    }

    return (
        <SidebarProvider>
            <AppSidebar
                conversations={conversations}
                setConversations={setConversations}
                currentConversationId={currentConversationId}
                setCurrentConversationId={setCurrentConversationId}
                updateConversationName={updateConversationName}
                deleteConversationID={deleteConversationID}
            />
            <SidebarInset>
                <div className="flex flex-1 flex-col">
                    <Header />
                    <MainContent />
                </div>
            </SidebarInset>
        </SidebarProvider>
    );
}