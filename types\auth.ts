import type { UserProfile } from './profile';
import type { DecodedDomainData } from './domain';

export interface AuthState {
    // Authentication data
    token: string | null;
    user_id: string | null;
    shop_id: string | null;
    // User information
    userInfo: UserProfile | null;

    website_id?: string;

    // App state
    isAuthenticated: boolean;
    isLoading: boolean;
    error: string | null;

    // Profile data
    profile: any;
    conversations: any[];

    domainData: DecodedDomainData | null;
}

export interface AuthActions {

    // Token management
    setToken: (token: string) => void;
    clearToken: () => void;
    getToken: () => string | null;

    // User data management
    setUserData: (data: { user_id?: string; shop_id?: string }) => void;
    setUserInfo: (userInfo: UserProfile | null) => void;


    // Profile management
    setProfile: (profile: any) => void;
    setConversations: (conversations: any[]) => void;



    // State management
    setLoading: (loading: boolean) => void;
    setError: (error: string | null) => void;

    // Clear all data
    clearAll: () => void;

    // Initialize from cookies
    initializeFromCookies: () => void;
}

// Domain related types
export interface DomainConfig {
    domain: string;
    allowed: boolean;
    config?: any;
    originalDomain?: string;
    mappedDomain?: string;
    prompt_id?: string;
}

export interface Domain {
    domainConfig: DomainConfig | null;
    isVerifying: boolean;
    isAllowed: boolean;
    error: string | null;
    verificationStep: string;
    retryVerification: () => void;
}

export interface GoogleAuthConfig {
    enabled: boolean;
    clientId?: string;
    clientSecret?: string;
}

// Cookie configuration
export interface CookieConfig {
    maxAge: number;
    path: string;
    sameSite: 'lax' | 'strict' | 'none';
    secure: boolean;
}

export const COOKIE_KEYS = {
    TOKEN: 'fchatai_token',
    USER_ID: 'fchatai_user_id',
    SHOP_ID: 'fchatai_shop_id',
} as const;

