import { useState, useCallback } from 'react';
import { getMessageConversation, postChat } from '@/services/messagechatService'; // Giả định file service
import type { Message } from '@/types';

export function useMessageChat(conversationId: string | null, promptId: string | null, shopId: string | null, userId: string, websiteId: string = '') {
  const [messages, setMessages] = useState<Message[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchMessages = useCallback(async () => {
    if (!conversationId || conversationId === '0' || !promptId || !shopId) {
      setMessages([]);
      setError(null);
      return [];
    }

    try {
      setIsLoading(true);
      const data = await getMessageConversation(conversationId);
      if (data.error) {
        throw new Error(data.msg || 'Không thể tải tin nhắn');
      }
      setMessages(data.data || []);
      return data.data;
    } catch (err: any) {
      setError(err.message || 'Không thể tải tin nhắn');
      return [];
    } finally {
      setIsLoading(false);
    }
  }, [conversationId, promptId, shopId]);

  const sendMessage = useCallback(
    async (content: string) => {
      if (!content.trim()) {
        throw new Error('Nội dung tin nhắn không được để trống');
      }

      if (!promptId || !shopId) {
        throw new Error('Thiếu thông tin cấu hình (prompt_id hoặc shop_id)');
      }

      try {
        const payload = {
          conversation_id: conversationId || '0',
          query: content,
          prompt_id: promptId,
          shop_id: shopId,
          user_id: userId,
          website_id: websiteId,
          version: '1.0', // Giả định version, cần điều chỉnh theo backend
        };
        const data = await postChat(payload);
        if (data.error) {
          throw new Error(data.msg || 'Gửi tin nhắn thất bại');
        }
        // Giả định API trả về tin nhắn mới hoặc danh sách tin nhắn
        if (Array.isArray(data.data)) {
          setMessages(data.data);
        } else {
          setMessages((prev) => [...prev, data.data]);
        }
        return data.data;
      } catch (err: any) {
        throw new Error(err.message || 'Gửi tin nhắn thất bại');
      }
    },
    [conversationId, promptId, shopId, userId, websiteId]
  );

  return { messages, isLoading, error, fetchMessages, sendMessage };
}