import { useMemo } from 'react';
import { useAuthStore } from '@/stores/authStore';

/**
 * Hook to replace ALL localStorage usage with global state
 * This ensures no component uses localStorage directly
 */
export const useGlobalState = () => {
  const authStore = useAuthStore();

  const auth = useMemo(() => ({
    token: authStore.token,
    user_id: authStore.user_id,
    shop_id: authStore.shop_id,
    // prompt_id và website_id lấy từ domainData, không lưu trong authStore
    userInfo: authStore.userInfo,
    isAuthenticated: authStore.isAuthenticated,
    profile: authStore.profile,
    conversations: authStore.conversations,

    // Actions
    setToken: authStore.setToken,
    setUserData: authStore.setUserData,
    setUserInfo: authStore.setUserInfo,
    setProfile: authStore.setProfile,
    setConversations: authStore.setConversations,
    clearAuth: authStore.clearAll,
    getToken: authStore.getToken,

  }), [
    authStore.token,
    authStore.user_id,
    authStore.shop_id,
    authStore.userInfo,
    authStore.isAuthenticated,
    authStore.profile,
    authStore.conversations,
    authStore.setToken,
    authStore.setUserData,
    authStore.setUserInfo,
    authStore.setProfile,
    authStore.setConversations,
    authStore.clearAll,
    authStore.getToken,
  ]);



  const clearAll = useMemo(() => () => {
    authStore.clearAll();
  }, [authStore.clearAll]);

  return useMemo(() => ({
    auth,
    clearAll,
  }), [auth, clearAll]);
};
