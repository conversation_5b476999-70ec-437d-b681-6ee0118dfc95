import { EP, QUERY } from "@/configs/constants/api";
import { getApiEndpoint } from "@/helpers/handleApiUrl";
import axiosClient from "@/lib/axios";
import { decryptAesCBC } from "@/lib/crypto";
import { useDomainStore } from "@/stores/domainStore";
import { setCookie } from "cookies-next/client";
import { DomainResponse, DecodedDomainData } from "@/types/domain";
export interface PayloadVerifyDomain {
    domain: string;
}
// Domain verification
export const verifyDomain = async (payload: PayloadVerifyDomain) => {
    const { setDecodedDomainData } = useDomainStore.getState();
    const { data } = await axiosClient(process.env.NEXT_PUBLIC_NODE_API_BACKEND_URL)
        .get<DomainResponse<string>>(getApiEndpoint([EP.API, EP.V1, EP.GLOBAL, EP.PROMPT, EP.DOMAIN], {
            [QUERY.DOMAIN]: payload.domain,
        }));

    const descryptedData = decryptAesCBC(data.data);
    if (!descryptedData) throw new Error("Giải mã thất bại")

    const parsed: DecodedDomainData = JSON.parse(descryptedData)

    // Lưu decoded data vào store
    setDecodedDomainData(parsed);
    return parsed;

};


export interface PayloadLoginGoogle {
    email: string;
    name: string;
    avatar: string;
    website_id: string;
    ref: string;
    google_id: string;
}

// Google login
export const loginWithGoogle = async (payload: PayloadLoginGoogle) => {
    const decodedDomainData = useDomainStore.getState().decodedDomainData;

    payload.website_id = decodedDomainData?.website.website_id || "";
    const { data } = await axiosClient(process.env.NEXT_PUBLIC_NODE_API_BACKEND_URL)
        .post<DomainResponse<string>>(getApiEndpoint([EP.API, EP.V1, EP.USER, EP.LOGIN, EP.GOOGLE]), payload);

    const token = data.data;
    setCookie("fchatai_token", token, {
        maxAge: 7 * 24 * 60 * 60, // 7 days
        path: '/',
        sameSite: 'lax',
    });
    return data;
};


export interface PayloadLoginEmail {
    email: string;
    password: string;
}
// Email/Password login
export const loginWithEmail = async (payload: PayloadLoginEmail) => {
    const { data } = await axiosClient(process.env.NEXT_PUBLIC_NODE_API_BACKEND_URL)
        .post<DomainResponse<string>>(getApiEndpoint([EP.API, EP.V1, EP.USER, EP.LOGIN]), payload);

    const token = data.data;
    setCookie("fchatai_token", token, {
        maxAge: 7 * 24 * 60 * 60, // 7 days
        path: '/',
        sameSite: 'lax',
    });
    return data;
};


