'use client';
import React, { useEffect, useRef } from "react";
import CryptoJ<PERSON> from "crypto-js";
import { dataImageType } from "@/types/photo";
import { useAuthStore } from "@/stores/authStore";
import { useDomain } from '@/components/providers/DomainProvider';

interface ImagePopupProps {
  isOpen: boolean;
  onClose: () => void;
  onImageSelect: (dataImages: dataImageType) => void;
}

const ImagePopup: React.FC<ImagePopupProps> = ({
  isOpen,
  onClose,
  onImageSelect,
}) => {
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  // const auth = useSelector((state: RootState) => state.auth.auth);
  // const shop = auth?.shop_data;
  const authStore = useAuthStore();
  const { domainData } = useDomain();

  // Chỉ tạo URL nếu có đủ data thật - lấy prompt_id từ domainData
  const promptId = domainData?.prompt?._id;
  const servicepackages = promptId ? "fchat" + promptId : null;
  const combinedString = (servicepackages && authStore.shop_id && authStore.token)
    ? `https://fchat.vn/_${authStore.shop_id}_${CryptoJS.MD5(servicepackages).toString()}_${authStore.token}`
    : null;
  const base64Encoded = combinedString ? btoa(combinedString) : null;
  const urlEncoded = base64Encoded ? encodeURIComponent(base64Encoded) : null;

  useEffect(() => {
    const iframeElement = document.getElementById(
      "iframe_photo"
    ) as HTMLIFrameElement | null;
    const openedWindow = iframeElement?.contentWindow;

    if (isOpen && openedWindow) {
      intervalRef.current = setInterval(() => {
        openedWindow.postMessage(
          "fchat_" + authStore.shop_id,
          "https://media.salekit.com?token=" + urlEncoded
        );
      }, 300);
    }

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [isOpen]);

  const receiveMessage = (event: MessageEvent) => {
    if (event.data && event.data.domain === "photo.salekit.com") {
      switch (event.data.status) {
        case "success":
          if (intervalRef.current) {
            clearInterval(intervalRef.current);
            intervalRef.current = null;
          }
          break;
        case "close":
          if (intervalRef.current) {
            clearInterval(intervalRef.current);
            intervalRef.current = null;
          }
          break;
        default:
          onImageSelect(event.data);
          onClose();
      }

      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    }
  };

  useEffect(() => {
    if (isOpen) {
      window.addEventListener("message", receiveMessage, false);
    }
    return () => {
      window.removeEventListener("message", receiveMessage, false);
    };
  }, [isOpen]);

  return isOpen ? (
    <div className="fixed inset-0 flex items-center justify-center z-50">
      <div className="bg-white rounded shadow-lg shop-images">
        <div className="shop-images-title">
          <div className="flex">
            {/* <IconDbImage color="#333" width="24" height="24" /> */}
            <h2 className="text-lg font-bold ml-2">Ảnh của shop</h2>
          </div>
          <button type="button" aria-label="Đóng" onClick={onClose}>
            {/* <IconRemove /> */}
          </button>
        </div>
        {urlEncoded ? (
          <iframe
            title="Ảnh của shop"
            id="iframe_photo"
            src={`https://media.salekit.com?auth=${urlEncoded}`}
            className="w-full h-96 border"
          />
        ) : (
          <div className="w-full h-96 border flex items-center justify-center text-gray-500">
            Không thể tải ảnh - thiếu thông tin xác thực
          </div>
        )}
      </div>
      <style>
        {`
                .shop-images {
                    max-width: 100%;
                    width: 990px;
                    min-width: 600px;
                    margin: auto;
                    z-index: 100;
                    border-radius: 10px;
                }
                .shop-images-title {
                    display: inline-flex;
                    justify-content: space-between;
                    align-items: center;
                    width: 100%;
                    background: #ffffff;
                    border-radius: 10px 10px 0 0;
                    padding: 10px 12px;
                }
                #iframe_photo {
                    position: relative;
                    border-bottom-left-radius: 10px;
                    border-bottom-right-radius: 10px;
                    height: 508px;
                }
                `}
      </style>
    </div>
  ) : null;
};

export default ImagePopup;
