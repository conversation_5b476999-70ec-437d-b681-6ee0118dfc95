"use client"

import React, { useState } from 'react';
import { DomainGuard } from '@/components/auth/DomainGuard';
import Header from '@/components/header/Header';
import MainContent from '@/components/home/<USER>';
import { AppSidebar } from '@/components/sidebar/AppSidebar';
import { SidebarProvider, SidebarInset } from '@/components/ui/sidebar';
import { useConversation } from '@/hooks/useConversation';

export default function Home() {
    const {
        conversations,
        updateConversationName,
        deleteConversationID,
        setConversations
    } = useConversation();

    const [currentConversationId, setCurrentConversationId] = useState<string | null>(null);

    // Main app interface
    return (
        <DomainGuard>
            <SidebarProvider>
                <AppSidebar
                    conversations={conversations}
                    setConversations={setConversations}
                    currentConversationId={currentConversationId}
                    setCurrentConversationId={setCurrentConversationId}
                    deleteConversationID={deleteConversationID}
                    updateConversationName={updateConversationName}
                />
                <SidebarInset>
                    <div className="flex flex-1 flex-col">
                        <Header />
                        <MainContent
                            conversations={conversations}
                            setConversations={setConversations}
                            currentConversationId={currentConversationId}
                            setCurrentConversationId={setCurrentConversationId}
                        />
                    </div>
                </SidebarInset>
            </SidebarProvider>
        </DomainGuard>
    );
}
