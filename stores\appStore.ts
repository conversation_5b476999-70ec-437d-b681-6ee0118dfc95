import { create } from 'zustand';
import { getCookie, setCookie, deleteCookie } from 'cookies-next/client';

// Types for all app data
interface AppState {
  // Domain configuration (replaces localStorage domain_config)
  domainConfig: any;

  // Google auth configuration
  googleAuthConfig: {
    enabled: boolean;
    clientId?: string;
    clientSecret?: string;
  };

  // Demo user state (replaces sessionStorage)
  demoUser: {
    isLoggedIn: boolean;
    userInfo: any;
  };

  // UI state
  isLoading: boolean;
  error: string | null;

  // Cross-tab sync state
  lastTokenUpdate: number;
}

interface AppActions {

  getPromptId: () => string | null; // <-- thêm dòng này
  getWebsiteId: () => string | null

  // UI state
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;

  // Cross-tab sync
  updateTokenTimestamp: () => void;
  getLastTokenUpdate: () => number;

  // Clear all data
  clearAll: () => void;
}

// Cookie keys for sensitive data
const APP_COOKIE_KEYS = {
  DOMAIN_CONFIG: 'fchatai_domain_config',
  GOOGLE_CONFIG: 'fchatai_google_config',
} as const;

// Cookie options
const COOKIE_OPTIONS = {
  maxAge: 7 * 24 * 60 * 60, // 7 days
  path: '/',
  sameSite: 'lax' as const,
  secure: process.env.NODE_ENV === 'production',
};


// Export cookie keys for use in other files
export { APP_COOKIE_KEYS, COOKIE_OPTIONS as APP_COOKIE_OPTIONS };
