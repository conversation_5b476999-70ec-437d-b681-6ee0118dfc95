"use client";

import { useEffect, useState, useCallback, useMemo } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { useAuthStore, authSelectors } from '@/stores/authStore';
import { useAppStore } from '@/stores/appStore';
import { useDomain } from '@/components/providers/DomainProvider';
import { useShallow } from 'zustand/react/shallow';

/**
 * Hook to manage authentication, domain verification, and related utilities
 */
export const useAuth = () => {
  const router = useRouter();
  const pathname = usePathname();
  const authStore = useAuthStore(
    useShallow((state) => ({
      token: state.token,
      user_id: state.user_id,
      shop_id: state.shop_id,
      prompt_id: state.prompt_id,
      website_id: state.getWebsiteId(),
      isAuthenticated: state.isAuthenticated,
      userInfo: state.userInfo,
      profile: state.profile,
      isLoading: state.isLoading,
      error: state.error,
      hasValidAuth: authSelectors.hasValidAuth(state),
      userCredits: authSelectors.userCredits(state),
      domainConfig: state.domainConfig,
      setToken: state.setToken,
      clearToken: state.clearToken,
      setUserData: state.setUserData,
      setUserInfo: state.setUserInfo,
      setProfile: state.setProfile,
      clearAll: state.clearAll,
      setDomainConfig: state.setDomainConfig,
      getToken: state.getToken,
      getWebsiteId: state.getWebsiteId,
    }))
  );
  const appStore = useAppStore();
  const { isVerifying: isDomainVerifying, isAllowed, error: domainError, domainConfig } = useDomain();
  const [isReady, setIsReady] = useState(false);
  const [authError, setAuthError] = useState<string | null>(null);
  const [redirectAttempts, setRedirectAttempts] = useState(0);
  const [hasRedirected, setHasRedirected] = useState(false);
  const MAX_REDIRECT_ATTEMPTS = 3;

  // Core auth data
  const authData = useMemo(
    () => ({
      token: authStore.getToken(),
      user_id: authStore.user_id,
      shop_id: authStore.shop_id,
      prompt_id: domainConfig?.prompt._id || authStore.prompt_id,
      website_id: authStore.getWebsiteId() || appStore.getWebsiteId(),
      isAuthenticated: authStore.isAuthenticated,
      userInfo: authStore.userInfo,
      profile: authStore.profile,
      isLoading: authStore.isLoading,
      hasValidAuth: authStore.hasValidAuth,
      userCredits: authStore.userCredits,
      hasToken: !!authStore.getToken(),
      hasRequiredData: !!(authStore.getToken() && authStore.user_id && authStore.shop_id),
      clearAuth: authStore.clearAll,
      setToken: authStore.setToken,
      setUserData: authStore.setUserData,
      setUserInfo: authStore.setUserInfo,
      setProfile: authStore.setProfile,
      setDomainConfig: authStore.setDomainConfig,
    }),
    [
      authStore.user_id,
      authStore.shop_id,
      authStore.prompt_id,
      authStore.isAuthenticated,
      authStore.userInfo,
      authStore.profile,
      authStore.isLoading,
      authStore.hasValidAuth,
      authStore.userCredits,
      authStore.getToken,
      authStore.clearAll,
      authStore.setToken,
      authStore.setUserData,
      authStore.setUserInfo,
      authStore.setProfile,
      authStore.setDomainConfig,
      appStore.getWebsiteId,
      domainConfig?.prompt._id,
    ]
  );

  // API headers
  const apiHeaders = useMemo(() => {
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
    };
    if (authData.token) {
      headers.token = authData.token;
    }
    if (authData.shop_id) {
      headers.shop_id = authData.shop_id;
    }
    return headers;
  }, [authData.token, authData.shop_id]);

  // Chat payload
  const chatPayload = useMemo(
    () => ({
      createPayload: (conversationId: string, query: string, promptId: string) => ({
        conversation_id: conversationId || '',
        query,
        prompt_id: promptId || authData.prompt_id || '',
        shop_id: authData.shop_id || '',
        version: 'gpt-4o-mini',
        user_id: authData.user_id || '',
        website_id: authData.website_id || '',
      }),
    }),
    [authData.user_id, authData.shop_id, authData.website_id, authData.prompt_id]
  );

  // Auth and domain state
  const authState = useMemo(() => {
    if (isDomainVerifying) {
      return {
        isReady: false,
        isAuthenticated: false,
        error: null,
        prompt_id: null,
        isDomainVerifying: true,
      };
    }

    if (!isAllowed) {
      return {
        isReady: false,
        isAuthenticated: false,
        error: domainError || 'Domain không được phép truy cập',
        prompt_id: null,
        isDomainVerifying: false,
      };
    }

    const finalPromptId = domainConfig?.prompt._id || authData.prompt_id;
    if (!finalPromptId) {
      return {
        isReady: false,
        isAuthenticated: false,
        error: null,
        prompt_id: null,
        isDomainVerifying: false,
      };
    }

    if (!authData.isAuthenticated || !authData.token) {
      return {
        isReady: false,
        isAuthenticated: false,
        error: null,
        prompt_id: finalPromptId,
        isDomainVerifying: false,
      };
    }

    return {
      isReady: true,
      isAuthenticated: true,
      error: null,
      prompt_id: finalPromptId,
      isDomainVerifying: false,
    };
  }, [
    isDomainVerifying,
    isAllowed,
    domainError,
    domainConfig?.prompt._id,
    authData.prompt_id,
    authData.isAuthenticated,
    authData.token,
  ]);

  // Check authentication and handle redirects
  const checkAuth = useCallback(() => {
    if (isDomainVerifying) {
      setIsReady(false);
      return;
    }

    if (!isAllowed) {
      setAuthError(domainError || 'Domain không được phép truy cập');
      setIsReady(false);
      return;
    }

    if (!authState.prompt_id) {
      setIsReady(false);
      return;
    }

    if (!authData.isAuthenticated || !authData.token) {
      if (redirectAttempts >= MAX_REDIRECT_ATTEMPTS || hasRedirected) {
        setAuthError('Quá nhiều lần chuyển hướng. Vui lòng tải lại trang.');
        setIsReady(false);
        return;
      }

      if (!hasRedirected && pathname !== '/login') {
        setRedirectAttempts((prev) => prev + 1);
        setHasRedirected(true);
        setTimeout(() => {
          router.push('/login');
        }, 100);
      }

      setIsReady(false);
      return;
    }

    if (redirectAttempts > 0 || hasRedirected) {
      setRedirectAttempts(0);
      setHasRedirected(false);
    }

    setAuthError(null);
    setIsReady(true);
  }, [
    isDomainVerifying,
    isAllowed,
    domainError,
    authState.prompt_id,
    authData.isAuthenticated,
    authData.token,
    router,
    pathname,
    redirectAttempts,
    hasRedirected,
  ]);

  useEffect(() => {
    checkAuth();
  }, [checkAuth]);

  useEffect(() => {
    const handleTokenSet = () => {
      setRedirectAttempts(0);
      setHasRedirected(false);
      setTimeout(() => {
        checkAuth();
      }, 100);
    };

    if (typeof window !== 'undefined') {
      window.addEventListener('tokenSet', handleTokenSet);
      return () => {
        window.removeEventListener('tokenSet', handleTokenSet);
      };
    }
  }, [checkAuth]);

  useEffect(() => {
    if (pathname === '/login') {
      setRedirectAttempts(0);
      setHasRedirected(false);
    }
  }, [pathname]);

  return {
    // Core auth data
    ...authData,
    // API headers
    apiHeaders,
    // Chat payload
    createChatPayload: chatPayload.createPayload,
    // Auth state
    isReady,
    authError,
    isDomainVerifying,
    isAllowed,
    // Domain auth state
    authState,
    // User credits
    userCredits: authData.userCredits,
  };
};

