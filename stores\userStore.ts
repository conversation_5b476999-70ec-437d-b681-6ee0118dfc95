import { create } from 'zustand';
import type { UserProfileResponse } from '@/types/profile';

interface UserStore {
    profile: UserProfileResponse | null;
    isLoading: boolean;
    setProfile: (profile: UserProfileResponse) => void;
    clearProfile: () => void;
}

export const useUserStore = create<UserStore>((set) => ({
    profile: null,
    isLoading: false,
    setProfile: (profile) => set({ profile }),
    clearProfile: () => set({ profile: null }),

}));
