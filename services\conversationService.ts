import { EP, QUERY } from "@/configs/constants/api";
import { getApiEndpoint } from "@/helpers/handleApiUrl";
import { COOKIE_KEYS } from "@/types/auth";
import { getCookie } from "cookies-next";
import axiosClient from "@/lib/axios";
import { ConversationResponse } from "@/types/conversation";

const token = getCookie(COOKIE_KEYS.TOKEN) as string;
//lấy danh sách conversation
export const getConversation = async (prompt_id: string) => {
    try {

        const { data } = await axiosClient(process.env.NEXT_PUBLIC_NODE_API_BACKEND_URL, token)
            .get<ConversationResponse>(getApiEndpoint([EP.API, EP.V1, EP.CONVERSATION, EP.PROMPT, EP.LIST], {
                [QUERY.PROMPT_ID]: prompt_id,
                [QUERY.PAGE]: 1,
                [QUERY.LIMIT]: 10
            }));
        console.log('getConversation', data);
        if (!Array.isArray(data.data.conversations)) return { ...data, data: { conversations: [], isFullPage: false } };
        return data;
    } catch (error) {
        return { error: true, status: 500, msg: 'Failed to load conversations', data: { conversations: [], isFullPage: false } };
    }
}

//cập nhật tên conversation
export interface PayloadUpdateConversation {
    id: string;
    name: string;
}
export const updateConversation = async (payload: PayloadUpdateConversation) => {
    const { data } = await axiosClient(process.env.NEXT_PUBLIC_NODE_API_BACKEND_URL, token)
        .post<ConversationResponse>(getApiEndpoint([EP.API, EP.V1, EP.CONVERSATION, EP.UPDATE]), payload);
    return data;
}

//xóa conversation
export const deleteConversation = async (conversationId: string) => {
    const { data } = await axiosClient(process.env.NEXT_PUBLIC_NODE_API_BACKEND_URL, token)
        .get<ConversationResponse>(getApiEndpoint([EP.API, EP.V1, EP.CONVERSATION, EP.DELETE_CONV], {
            [QUERY.CONVERSATION_ID]: conversationId
        }));
    return data;
}
