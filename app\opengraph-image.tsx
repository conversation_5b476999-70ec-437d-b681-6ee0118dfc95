import { ImageResponse } from 'next/og'

// Route segment config
export const runtime = 'edge'

// Image metadata - sẽ được dynamic từ domain data
export const alt = 'FChat AI - Trợ lý AI thông minh'
export const size = {
  width: 1200,
  height: 630,
}
export const contentType = 'image/png'

// Image generation
export default async function Image() {
  return new ImageResponse(
    (
      <div tw="h-full w-full flex flex-col items-center justify-center bg-dark-bg bg-brand-gradient">
        <div tw="flex items-center justify-center mb-10">
          <div tw="w-[120px] h-[120px] bg-white rounded-lg flex items-center justify-center text-[80px] font-bold mr-8 text-blue-600">
            F
          </div>
          <div tw="text-[72px] font-bold text-white">
            FChat AI
          </div>
        </div>
        <div tw="text-[36px] text-white text-center max-w-[800px] leading-tight">
          Tr<PERSON> lý AI thông minh
        </div>
        <div tw="text-[24px] text-center mt-5 text-white-80">
          Powered by AI
        </div>
      </div>
    ),
    {
      ...size,
    }
  )
}
