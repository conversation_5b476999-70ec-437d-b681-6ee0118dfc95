"use client"

import { useEffect } from 'react';

import { useAuthStore } from '@/stores/authStore';
import { getUserProfile } from '@/services/userService';
import { getCookie } from 'cookies-next/client';
import { COOKIE_KEYS } from '@/types/auth';

interface AuthProviderProps {
  children: React.ReactNode;
}

/**
 * AuthProvider - Initializes auth store from cookies on app start and loads user profile
 */
export function AuthProvider({ children }: AuthProviderProps) {
  const initializeFromCookies = useAuthStore((state) => state.initializeFromCookies);
  const isAuthenticated = useAuthStore((state) => state.isAuthenticated);

  useEffect(() => {
    // Initialize auth store from cookies
    initializeFromCookies();
  }, [initializeFromCookies]);

  // Load user profile when authenticated
  useEffect(() => {
    const loadUserProfile = async () => {
      // Check if user is authenticated and has token
      const authToken = getCookie(COOKIE_KEYS.TOKEN);

      if (isAuthenticated && authToken) {
        try {
          await getUserProfile();
        } catch (error) {
          console.error('❌ Failed to load user profile:', error);
        }
      }
    };

    // Add a small delay to ensure auth state is properly initialized
    const timeoutId = setTimeout(loadUserProfile, 500);

    return () => clearTimeout(timeoutId);
  }, [isAuthenticated]);

  return <>{children}</>;
}
